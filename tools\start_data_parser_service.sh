#!/bin/bash

# 数据解析器服务启动脚本

echo "启动数据解析器服务..."

# 检查Python3是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3，请先安装Python3"
    exit 1
fi

# 检查是否安装了必要的依赖
echo "检查依赖包..."
python3 -c "import watchdog" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
fi

# 创建data目录（如果不存在）
if [ ! -d "data" ]; then
    echo "创建data目录..."
    mkdir -p data
fi

# 启动服务
echo "启动数据解析器服务..."
python3 src/services/data_parser_service.py