#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
D8命令数据解析器
根据D6-D7-D8单元程序版本信息协议解析文档，解析C相单元程序版本信息
"""

import json
from datetime import datetime
from typing import List, Dict, Any


class D8DataParser:
    """
    D8命令数据解析器类
    用于解析D8命令返回的C相单元程序版本信息数据
    """
    
    def __init__(self):
        """
        初始化解析器
        """
        self.command_code = 0xD8
        self.phase_name = "C相"
        self.total_units = 12
    
    def calculate_checksum(self, data: bytes) -> int:
        """
        计算校验和（单字节和校验）
        
        Args:
            data: 需要计算校验和的字节数据
            
        Returns:
            int: 校验和值（低8位）
        """
        return sum(data) & 0xFF
    
    def verify_checksum(self, frame_data: bytes) -> bool:
        """
        验证校验和
        
        Args:
            frame_data: 完整的帧数据
            
        Returns:
            bool: 校验和是否正确
        """
        if len(frame_data) < 3:
            return False
            
        received_checksum = frame_data[-3]  # 倒数第3字节是校验和
        calculated_checksum = self.calculate_checksum(frame_data[:-3])
        return received_checksum == calculated_checksum
    
    def parse_unit_version(self, unit_data: bytes) -> Dict[str, Any]:
        """
        解析单个单元的版本信息
        
        Args:
            unit_data: 2字节的单元数据
            
        Returns:
            dict: 解析后的版本信息
        """
        if len(unit_data) != 2:
            raise ValueError(f"单元数据长度必须为2字节，实际为{len(unit_data)}字节")
        
        low_byte, high_byte = unit_data[0], unit_data[1]
        raw_hex = f"{low_byte:02X} {high_byte:02X}"
        
        # 检查是否为旁路状态
        if unit_data == b'\x00\x00':
            return {
                'raw_bytes': raw_hex,
                'version': '旁路',
                'major': None,
                'minor': None,
                'patch': None,
                'status': '旁路',
                'is_active': False
            }
        
        # 小头排列组合为16位数值（0x高字节低字节）
        combined = (high_byte << 8) | low_byte
        
        # 按BCD码解析，每4位代表一个数字
        # 0x4124 -> 4, 12, 4
        major = (combined >> 12) & 0x0F          # 第1个BCD数字（千位）
        minor_tens = (combined >> 8) & 0x0F    # 第2个BCD数字（百位）
        minor_ones = (combined >> 4) & 0x0F    # 第3个BCD数字（十位）
        patch = combined & 0x0F                # 第4个BCD数字（个位）
        
        # 组合次版本号：百位和十位组合成12
        minor = minor_tens * 10 + minor_ones
        
        version_str = f"{major}.{minor}.{patch}"
        
        return {
            'raw_bytes': raw_hex,
            'version': version_str,
            'major': major,
            'minor': minor,
            'patch': patch,
            'status': '激活',
            'is_active': True
        }
    
    def parse_d8_response(self, response_data: bytes) -> Dict[str, Any]:
        """
        解析D8响应数据
        
        Args:
            response_data: 完整的响应帧数据（48字节）
            
        Returns:
            dict: 解析后的C相单元版本信息
        """
        if len(response_data) != 48:
            raise ValueError(f"D8响应帧长度必须为48字节，实际为{len(response_data)}字节")
        
        # 验证帧头
        if response_data[0:4] != b'\xEB\x90\x01\x01':
            raise ValueError(f"无效的帧头: {response_data[0:4].hex().upper()}")
        
        # 验证命令码
        command = response_data[4]
        if command != self.command_code:
            raise ValueError(f"命令码应为0x{self.command_code:02X}，实际为0x{command:02X}")
        
        # 验证数据长度
        data_length = response_data[5]
        if data_length != 0x24:
            raise ValueError(f"数据长度应为0x24，实际为0x{data_length:02X}")
        
        # 验证校验和
        checksum_valid = self.verify_checksum(response_data)
        
        # 提取版本数据（字节9-32，索引9-33）
        version_data = response_data[9:33]
        
        # 解析12个单元的版本信息
        units_info = {}
        active_count = 0
        
        for unit_index in range(self.total_units):
            start_pos = unit_index * 2
            unit_data = version_data[start_pos:start_pos + 2]
            
            unit_number = unit_index + 1
            unit_key = f"unit_{unit_number:02d}"
            
            unit_info = self.parse_unit_version(unit_data)
            units_info[unit_key] = unit_info
            
            if unit_info['is_active']:
                active_count += 1
        
        return {
            'phase': self.phase_name,
            'command': f"0x{command:02X}",
            'data_length': data_length,
            'total_units': self.total_units,
            'active_units': active_count,
            'bypassed_units': self.total_units - active_count,
            'units': units_info,
            'checksum_valid': checksum_valid,
            'raw_response': response_data.hex().upper()
        }
    
    def parse_d8_data(self, data_hex_formatted: List[str], timestamp: str = None) -> List[Dict[str, Any]]:
        """
        解析D8命令的data_hex_formatted数据
        
        Args:
            data_hex_formatted: D8命令返回的格式化十六进制数据（12个32位十六进制值）
            timestamp: 时间戳，如果不提供则使用当前时间
            
        Returns:
            包含C相单元版本信息的JSON格式列表
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        result = []
        
        if len(data_hex_formatted) != 12:
            raise ValueError(f"D8命令数据格式错误，应包含12个32位十六进制值，实际收到{len(data_hex_formatted)}个")
        
        # 将12个32位十六进制值转换为字节数据（共48字节）
        response_data = bytes()
        for hex_str in data_hex_formatted:
            if hex_str.startswith('0x'):
                hex_str = hex_str[2:]
            if len(hex_str) == 8:  # 8个十六进制字符 = 4字节
                response_data += bytes.fromhex(hex_str)
            else:
                raise ValueError(f"无效的32位十六进制值: {hex_str}")
        
        if len(response_data) != 48:
            raise ValueError(f"响应数据长度错误，应为48字节，实际为{len(response_data)}字节")
        
        # 解析响应数据
        parsed_data = self.parse_d8_response(response_data)
        
        # 转换为标准输出格式
        for unit_key, unit_info in parsed_data['units'].items():
            unit_number = int(unit_key.split('_')[1])
            
            result.append({
                "id": f"D8_unit_{unit_number:02d}",
                "name": f"{self.phase_name}第{unit_number}单元版本",
                "ts": timestamp,
                "value": unit_info['version'],
                "status": unit_info['status'],
                "raw_data": unit_info['raw_bytes']
            })
        
        # 添加汇总信息
        result.append({
            "id": "D8_summary",
            "name": f"{self.phase_name}单元状态汇总",
            "ts": timestamp,
            "value": f"激活:{parsed_data['active_units']}/旁路:{parsed_data['bypassed_units']}",
            "total_units": parsed_data['total_units'],
            "active_units": parsed_data['active_units'],
            "bypassed_units": parsed_data['bypassed_units'],
            "checksum_valid": parsed_data['checksum_valid']
        })
        
        return result


def main():
    """主函数：从realtime_data.json读取D8命令数据并解析，输出MQTT格式的JSON"""
    import json
    import os
    from datetime import datetime

    # 构建文件路径
    data_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'data')
    input_file = os.path.join(data_dir, 'realtime_data.json')
    output_file = os.path.join(data_dir, 'd8_parsed_data.json')

    try:
        # 读取realtime_data.json
        with open(input_file, 'r', encoding='utf-8') as f:
            realtime_data = json.load(f)

        # 查找D8命令数据
        d8_data = next((item for item in realtime_data if item.get('command') == 'D8'), None)
        
        if not d8_data:
            print("未找到D8命令数据")
            return

        parser = D8DataParser()
        
        # 优先使用raw_hex解析完整响应帧
        raw_hex = d8_data.get('raw_hex', '')
        if raw_hex and len(raw_hex) >= 96:  # 48字节 = 96个十六进制字符
            response_data = bytes.fromhex(raw_hex)
            parsed_result = parser.parse_d8_response(response_data)
        else:
            # 回退到data_hex_formatted解析
            data_hex = d8_data.get('data_hex_formatted', '')
            if data_hex:
                # 取前12个有效数据（12单元×2字节 = 24字节 = 48个十六进制字符）
                version_data = bytes.fromhex(data_hex.replace(' ', '')[:48])
                parsed_result = parser.parse_d8_data(version_data)
            else:
                print("未找到有效的D8数据")
                return

        # 转换为MQTT消息格式（仅版本信息）
        timestamp = datetime.fromtimestamp(d8_data.get('timestamp', datetime.now().timestamp())).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        mqtt_messages = []
        for i in range(1, 13):
            unit_key = f'unit_{i:02d}'
            unit_data = parsed_result['units'][unit_key]
            
            # 仅保留单元版本信息
            mqtt_messages.append({
                "id": f"D8_{i}_version",
                "name": f"C相单元{i}版本号",
                "ts": timestamp,
                "value": unit_data['version']
            })

        # 保存MQTT格式结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(mqtt_messages, f, ensure_ascii=False, indent=2)
        
        print(f"D8数据解析完成，MQTT格式结果已保存到: {output_file}")
        print(f"生成消息数量: {len(mqtt_messages)}")
        print(f"解析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    except Exception as e:
        print(f"处理D8数据时发生错误: {e}")
        import traceback
        traceback.print_exc()


# 使用示例
if __name__ == "__main__":
    # 如果直接运行，执行主函数
    main()
    
    # 也可以测试示例数据
    # parser = D8DataParser()
    # response_data = bytes.fromhex(
    #     "EB900101D824000000244124412441244124412441244124412441244124412441"
    #     "0000000000000000000000000000000000000000000000000000000000000000"
    #     "6BAAAB"
    # )
    # data_hex_formatted = [
    #     f"0x{response_data[i*4:(i+1)*4].hex()}" 
    #     for i in range(12)
    # ]
    # result = parser.parse_d8_data(data_hex_formatted)
    # print(json.dumps(result, indent=2, ensure_ascii=False))