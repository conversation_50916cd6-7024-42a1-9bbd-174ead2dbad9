#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT消息推送服务
用于将C0解析数据推送到MQTT服务器
参考FastBee_sdk.py进行优化
"""

import json
import time
import logging
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

try:
    import paho.mqtt.client as mqtt
except ImportError:
    print("请安装paho-mqtt库: pip install paho-mqtt")
    exit(1)

class MQTTPublisher:
    """
    MQTT消息推送服务类
    参考FastBee_sdk.py优化的版本
    """
    
    def __init__(self, config_file: str = "docs/C0Mqtt推送"):
        """
        初始化MQTT推送服务
        
        Args:
            config_file: MQTT配置文件路径
        """
        self.config_file = config_file
        self.mqtt_config = None
        self.client = None
        self.connected = False
        self.connection_status = -1  # 连接状态标志位，参考FastBee_sdk.py
        self.publish_timer = None  # 定时发布定时器
        self.monitor_interval = 30  # 监控间隔（秒）
        self.max_retry_count = 5  # 最大重试次数
        self.retry_count = 0  # 当前重试次数
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # 加载MQTT配置
        self.load_mqtt_config()
        
        # 初始化MQTT客户端
        self.init_mqtt_client()
    
    def load_mqtt_config(self):
        """
        加载MQTT配置信息
        """
        try:
            config_path = Path(self.config_file)
            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
            
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取JSON配置
            lines = content.strip().split('\n')
            json_line = None
            ip_line = None
            
            for line in lines:
                line = line.strip()
                if line.startswith('{') and line.endswith('}'):
                    json_line = line
                elif line.startswith('IP为：'):
                    ip_line = line
            
            if not json_line:
                self.logger.error(f"配置文件内容: {content}")
                raise ValueError("未找到MQTT配置JSON")
            
            self.mqtt_config = json.loads(json_line)
            
            # 提取IP地址
            if ip_line:
                ip = ip_line.replace('IP为：', '').strip()
                self.mqtt_config['host'] = ip
            else:
                raise ValueError("未找到MQTT服务器IP地址")
            
            self.logger.info(f"MQTT配置加载成功: {self.mqtt_config['host']}:{self.mqtt_config['port']}")
            
        except Exception as e:
            self.logger.error(f"加载MQTT配置失败: {e}")
            raise
    
    def init_mqtt_client(self):
        """
        初始化MQTT客户端
        参考FastBee_sdk.py的客户端配置
        """
        try:
            # 创建MQTT客户端，使用配置中的clientId
            self.client = mqtt.Client(client_id=self.mqtt_config['clientId'])
            
            # 设置用户名和密码
            self.client.username_pw_set(
                username=self.mqtt_config['username'],
                password=self.mqtt_config['passwd']
            )
            
            # 设置回调函数
            self.client.on_connect = self.on_connect
            self.client.on_disconnect = self.on_disconnect
            self.client.on_publish = self.on_publish
            self.client.on_message = self.on_message  # 添加消息接收回调
            
            # 设置连接保持时间和重连参数
            self.client.reconnect_delay_set(min_delay=1, max_delay=120)
            
            self.logger.info("MQTT客户端初始化成功")
            
        except Exception as e:
            self.logger.error(f"MQTT客户端初始化失败: {e}")
            raise
    
    def on_connect(self, client, userdata, flags, rc):
        """
        MQTT连接回调函数
        参考FastBee_sdk.py的连接处理逻辑
        
        Args:
            client: MQTT客户端实例
            userdata: 用户数据
            flags: 连接标志
            rc: 连接结果代码
        """
        if rc == 0:
            self.connected = True
            self.connection_status = 0
            self.retry_count = 0  # 重置重试计数
            self.logger.info("MQTT连接成功")
            
            # 订阅控制主题（如果需要接收控制指令）
            subscribe_topic = self.mqtt_config.get('subscribeTopic')
            if subscribe_topic:
                client.subscribe(subscribe_topic, 1)
                self.logger.info(f"订阅主题: {subscribe_topic}")
                
        else:
            self.connected = False
            self.connection_status = rc
            self.retry_count += 1
            error_messages = {
                1: "连接失败-不正确的协议版本",
                2: "连接失败-无效的客户端标识符", 
                3: "连接失败-服务器不可用",
                4: "连接失败-错误的用户名或密码",
                5: "连接失败-未授权"
            }
            error_msg = error_messages.get(rc, f"连接失败-未知错误({rc})")
            self.logger.error(error_msg)
            
            # 如果重试次数未超过最大值，则尝试重连
            if self.retry_count < self.max_retry_count:
                self.logger.info(f"3秒后进行第{self.retry_count}次重连...")
                threading.Timer(3.0, self.reconnect).start()
            else:
                self.logger.error(f"重连失败次数已达到最大值({self.max_retry_count})，停止重连")
    
    def on_disconnect(self, client, userdata, rc):
        """
        MQTT断开连接回调函数
        
        Args:
            client: MQTT客户端实例
            userdata: 用户数据
            rc: 断开连接代码
        """
        self.connected = False
        self.connection_status = -1
        if rc != 0:
            self.logger.warning(f"MQTT意外断开连接，代码: {rc}")
            # 意外断开时尝试重连
            if self.retry_count < self.max_retry_count:
                self.logger.info("5秒后尝试重连...")
                threading.Timer(5.0, self.reconnect).start()
        else:
            self.logger.info("MQTT正常断开连接")
    
    def on_message(self, client, userdata, msg):
        """
        MQTT消息接收回调函数
        参考FastBee_sdk.py的消息处理
        
        Args:
            client: MQTT客户端实例
            userdata: 用户数据
            msg: 接收到的消息
        """
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            self.logger.info(f"收到消息 - 主题: {topic}, 内容: {payload}")
            
            # 这里可以添加对接收到的控制指令的处理逻辑
            # 例如：远程控制、参数配置等
            
        except Exception as e:
            self.logger.error(f"处理接收消息时出错: {e}")
    
    def reconnect(self):
        """
        重连MQTT服务器
        参考FastBee_sdk.py的重连机制
        """
        try:
            if not self.connected and self.retry_count < self.max_retry_count:
                self.retry_count += 1
                self.logger.info(f"尝试第{self.retry_count}次重连MQTT服务器...")
                self.connect()
        except Exception as e:
            self.logger.error(f"重连时出错: {e}")
    
    def on_publish(self, client, userdata, mid):
        """
        MQTT消息发布回调函数
        
        Args:
            client: MQTT客户端实例
            userdata: 用户数据
            mid: 消息ID
        """
        self.logger.debug(f"消息发布成功，消息ID: {mid}")
    
    def connect(self) -> bool:
        """
        连接到MQTT服务器
        
        Returns:
            连接是否成功
        """
        try:
            host = self.mqtt_config['host']
            port = int(self.mqtt_config['port'])
            
            self.logger.info(f"正在连接MQTT服务器: {host}:{port}")
            self.client.connect(host, port, 60)
            
            # 启动网络循环
            self.client.loop_start()
            
            # 等待连接建立
            timeout = 10
            start_time = time.time()
            while not self.connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if self.connected:
                self.logger.info("MQTT连接建立成功")
                return True
            else:
                self.logger.error("MQTT连接超时")
                return False
                
        except Exception as e:
            self.logger.error(f"MQTT连接失败: {e}")
            return False
    
    def disconnect(self):
        """
        断开MQTT连接
        参考FastBee_sdk.py的断开连接处理
        """
        try:
            # 停止定期发布
            self.stop_periodic_publish()
            
            if self.client and self.connected:
                self.client.disconnect()
                self.client.loop_stop()
                self.logger.info("MQTT连接已断开")
            
            self.connected = False
            self.connection_status = -1
            
        except Exception as e:
            self.logger.error(f"断开MQTT连接时出错: {e}")
    
    def get_connection_status(self):
        """
        获取连接状态
        参考FastBee_sdk.py的状态查询
        
        Returns:
            dict: 连接状态信息
        """
        return {
            'connected': self.connected,
            'connection_status': self.connection_status,
            'retry_count': self.retry_count,
            'max_retry_count': self.max_retry_count,
            'client_id': self.mqtt_config.get('clientId', '') if self.mqtt_config else '',
            'server': f"{self.mqtt_config.get('host', '')}:{self.mqtt_config.get('port', '')}" if self.mqtt_config else ''
        }
    
    def health_check(self):
        """
        健康检查
        参考FastBee_sdk.py的健康检查机制
        
        Returns:
            bool: 健康状态
        """
        try:
            if not self.connected:
                self.logger.warning("健康检查失败: MQTT未连接")
                return False
            
            # 检查客户端状态
            if not self.client or not self.client.is_connected():
                self.logger.warning("健康检查失败: 客户端状态异常")
                return False
            
            self.logger.debug("健康检查通过")
            return True
            
        except Exception as e:
            self.logger.error(f"健康检查异常: {e}")
            return False
    
    def load_d6_data(self, data_file: str = "data/d6_parsed_data.json") -> Optional[list]:
        """
        加载D6解析数据
        
        Args:
            data_file: D6数据文件路径
            
        Returns:
            D6解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D6数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D6数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D6数据失败: {e}")
            return None

    def load_d7_data(self, data_file: str = "data/d7_parsed_data.json") -> Optional[list]:
        """
        加载D7解析数据
        
        Args:
            data_file: D7数据文件路径
            
        Returns:
            D7解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D7数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D7数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D7数据失败: {e}")
            return None

    def load_d8_data(self, data_file: str = "data/d8_parsed_data.json") -> Optional[list]:
        """
        加载D8解析数据
        
        Args:
            data_file: D8数据文件路径
            
        Returns:
            D8解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D8数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D8数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D8数据失败: {e}")
            return None

    def load_d0_data(self, data_file: str = "data/d0_parsed_data.json") -> Optional[list]:
        """
        加载D0解析数据
        
        Args:
            data_file: D0数据文件路径
            
        Returns:
            D0解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D0数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D0数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D0数据失败: {e}")
            return None

    def load_d1_data(self, data_file: str = "data/d1_parsed_data.json") -> Optional[list]:
        """
        加载D1解析数据
        
        Args:
            data_file: D1数据文件路径
            
        Returns:
            D1解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D1数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D1数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D1数据失败: {e}")
            return None

    def load_d2_data(self, data_file: str = "data/d2_parsed_data.json") -> Optional[list]:
        """
        加载D2解析数据
        
        Args:
            data_file: D2数据文件路径
            
        Returns:
            D2解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D2数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D2数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D2数据失败: {e}")
            return None

    def create_d6_mqtt_message(self, d6_data: list) -> list:
        """
        创建D6 MQTT消息体
        
        Args:
            d6_data: D6解析数据
            
        Returns:
            D6 MQTT消息体
        """
        return d6_data

    def create_d7_mqtt_message(self, d7_data: list) -> list:
        """
        创建D7 MQTT消息体
        
        Args:
            d7_data: D7解析数据
            
        Returns:
            D7 MQTT消息体
        """
        return d7_data

    def create_d8_mqtt_message(self, d8_data: list) -> list:
        """
        创建D8 MQTT消息体
        
        Args:
            d8_data: D8解析数据
            
        Returns:
            D8 MQTT消息体
        """
        return d8_data

    def create_d0_mqtt_message(self, d0_data: list) -> list:
        """
        创建D0 MQTT消息体
        
        Args:
            d0_data: D0解析数据
            
        Returns:
            D0 MQTT消息体
        """
        return d0_data

    def create_d1_mqtt_message(self, d1_data: list) -> list:
        """
        创建D1 MQTT消息体
        
        Args:
            d1_data: D1解析数据
            
        Returns:
            D1 MQTT消息体
        """
        return d1_data

    def create_d2_mqtt_message(self, d2_data: list) -> list:
        """
        创建D2 MQTT消息体
        
        Args:
            d2_data: D2解析数据
            
        Returns:
            D2 MQTT消息体
        """
        return d2_data

    def publish_d6_data(self, data_file: str = "data/d6_parsed_data.json") -> bool:
        """
        发布D6数据到MQTT
        
        Args:
            data_file: D6数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D6消息")
            return False
        
        # 加载D6数据
        d6_data = self.load_d6_data(data_file)
        if not d6_data:
            return False
        
        # 创建D6消息体
        message = self.create_d6_mqtt_message(d6_data)
        
        try:
            # 发布D6数据到指定主题
            topic = "/196/D19822J4J92OT/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D6数据发布成功到主题: {topic}")
                self.logger.debug(f"D6消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D6数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D6数据时发生错误: {e}")
            return False

    def publish_d7_data(self, data_file: str = "data/d7_parsed_data.json") -> bool:
        """
        发布D7数据到MQTT
        
        Args:
            data_file: D7数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D7消息")
            return False
        
        # 加载D7数据
        d7_data = self.load_d7_data(data_file)
        if not d7_data:
            return False
        
        # 创建D7消息体
        message = self.create_d7_mqtt_message(d7_data)
        
        try:
            # 发布D7数据到指定主题
            topic = "/196/D19822J4J92OT/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D7数据发布成功到主题: {topic}")
                self.logger.debug(f"D7消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D7数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D7数据时发生错误: {e}")
            return False

    def publish_d8_data(self, data_file: str = "data/d8_parsed_data.json") -> bool:
        """
        发布D8数据到MQTT
        
        Args:
            data_file: D8数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D8消息")
            return False
        
        # 加载D8数据
        d8_data = self.load_d8_data(data_file)
        if not d8_data:
            return False
        
        # 创建D8消息体
        message = self.create_d8_mqtt_message(d8_data)
        
        try:
            # 发布D8数据到指定主题
            topic = "/196/D19822J4J92OT/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D8数据发布成功到主题: {topic}")
                self.logger.debug(f"D8消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D8数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D8数据时发生错误: {e}")
            return False

    def publish_d0_data(self, data_file: str = "data/d0_parsed_data.json") -> bool:
        """
        发布D0数据到MQTT
        
        Args:
            data_file: D0数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D0消息")
            return False
        
        # 加载D0数据
        d0_data = self.load_d0_data(data_file)
        if not d0_data:
            return False
        
        # 创建D0消息体
        message = self.create_d0_mqtt_message(d0_data)
        
        try:
            # 发布D0数据到指定主题
            topic = "/195/D19DQ66713XIJ/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D0数据发布成功到主题: {topic}")
                self.logger.debug(f"D0消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D0数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D0数据时发生错误: {e}")
            return False

    def publish_d1_data(self, data_file: str = "data/d1_parsed_data.json") -> bool:
        """
        发布D1数据到MQTT
        
        Args:
            data_file: D1数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D1消息")
            return False
        
        # 加载D1数据
        d1_data = self.load_d1_data(data_file)
        if not d1_data:
            return False
        
        # 创建D1消息体
        message = self.create_d1_mqtt_message(d1_data)
        
        try:
            # 发布D1数据到指定主题
            topic = "/195/D19DQ66713XIJ/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D1数据发布成功到主题: {topic}")
                self.logger.debug(f"D1消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D1数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D1数据时发生错误: {e}")
            return False

    def publish_d2_data(self, data_file: str = "data/d2_parsed_data.json") -> bool:
        """
        发布D2数据到MQTT
        
        Args:
            data_file: D2数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D2消息")
            return False
        
        # 加载D2数据
        d2_data = self.load_d2_data(data_file)
        if not d2_data:
            return False
        
        # 创建D2消息体
        message = self.create_d2_mqtt_message(d2_data)
        
        try:
            # 发布D2数据到指定主题
            topic = "/195/D19DQ66713XIJ/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D2数据发布成功到主题: {topic}")
                self.logger.debug(f"D2消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D2数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D2数据时发生错误: {e}")
            return False

    def publish_all_types_data(self, c0_file: str = "data/c0_parsed_data.json", 
                              c5_file: str = "data/c5_parsed_data.json",
                              d0_file: str = "data/d0_parsed_data.json",
                              d1_file: str = "data/d1_parsed_data.json",
                              d2_file: str = "data/d2_parsed_data.json",
                              d6_file: str = "data/d6_parsed_data.json",
                              d7_file: str = "data/d7_parsed_data.json",
                              d8_file: str = "data/d8_parsed_data.json") -> bool:
        """
        同时发布所有类型数据（C0、C5、D0、D1、D2、D6、D7、D8）
        
        Args:
            c0_file: C0数据文件路径
            c5_file: C5数据文件路径
            d0_file: D0数据文件路径
            d1_file: D1数据文件路径
            d2_file: D2数据文件路径
            d6_file: D6数据文件路径
            d7_file: D7数据文件路径
            d8_file: D8数据文件路径
            
        Returns:
            发布是否成功
        """
        success_c0 = self.publish_c0_data(c0_file)
        success_c5 = self.publish_c5_data(c5_file)
        success_d0 = self.publish_d0_data(d0_file)
        success_d1 = self.publish_d1_data(d1_file)
        success_d2 = self.publish_d2_data(d2_file)
        success_d6 = self.publish_d6_data(d6_file)
        success_d7 = self.publish_d7_data(d7_file)
        success_d8 = self.publish_d8_data(d8_file)
        
        return success_c0 and success_c5 and success_d0 and success_d1 and success_d2 and success_d6 and success_d7 and success_d8

    

    def _schedule_next_publish_all_types(self):
        """
        调度下一次发布（同时发布所有类型数据）
        """
        def publish_task():
            try:
                if self.connected:
                    success = self.publish_all_types_data(
                        getattr(self, 'c0_file', "data/c0_parsed_data.json"),
                        getattr(self, 'c5_file', "data/c5_parsed_data.json"),
                        getattr(self, 'd0_file', "data/d0_parsed_data.json"),
                        getattr(self, 'd1_file', "data/d1_parsed_data.json"),
                        getattr(self, 'd2_file', "data/d2_parsed_data.json"),
                        getattr(self, 'd6_file', "data/d6_parsed_data.json"),
                        getattr(self, 'd7_file', "data/d7_parsed_data.json"),
                        getattr(self, 'd8_file', "data/d8_parsed_data.json")
                    )
                    if success:
                        self.logger.info("定期发布成功（C0+C5+D0+D1+D2+D6+D7+D8）")
                    else:
                        self.logger.warning("定期发布失败（C0+C5+D0+D1+D2+D6+D7+D8）")
                else:
                    self.logger.warning("MQTT未连接，跳过本次发布")
                    # 如果未连接，尝试重连
                    if self.retry_count < self.max_retry_count:
                        self.reconnect()
                        
            except Exception as e:
                self.logger.error(f"定期发布出错: {e}")
            
            # 调度下一次发布
            self._schedule_next_publish_all_types()
        
        # 创建定时器
        self.publish_timer = threading.Timer(self.monitor_interval, publish_task)
        self.publish_timer.daemon = True
        self.publish_timer.start()

    def load_c0_data(self, data_file: str = "data/c0_parsed_data.json") -> Optional[list]:
        """
        加载C0解析数据
        
        Args:
            data_file: C0数据文件路径
            
        Returns:
            C0解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"C0数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载C0数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载C0数据失败: {e}")
            return None
    
    def load_c5_data(self, data_file: str = "data/c5_parsed_data.json") -> Optional[list]:
        """
        加载C5解析数据
        
        Args:
            data_file: C5数据文件路径
            
        Returns:
            C5解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"C5数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载C5数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载C5数据失败: {e}")
            return None
    
    def create_mqtt_message(self, c0_data: list) -> list:
        """
        创建MQTT消息体
        
        Args:
            c0_data: C0解析数据
            
        Returns:
            MQTT消息体（直接返回c0_data原始格式）
        """
        return c0_data

    def publish_c0_data(self, data_file: str = "data/c0_parsed_data.json") -> bool:
        """
        发布C0数据到MQTT
        
        Args:
            data_file: C0数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布消息")
            return False
        
        # 加载C0数据
        c0_data = self.load_c0_data(data_file)
        if not c0_data:
            return False
        
        # 创建消息体
        message = self.create_mqtt_message(c0_data)
        
        try:
            # 发布消息
            topic = self.mqtt_config['reportTopic']
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"C0数据发布成功到主题: {topic}")
                self.logger.debug(f"消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"C0数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布C0数据时发生错误: {e}")
            return False
    
    def start_periodic_publish(self, interval: int = 30, data_file: str = "data/c0_parsed_data.json"):
        """
        启动定期发布服务
        参考FastBee_sdk.py的定时器机制
        
        Args:
            interval: 发布间隔（秒）
            data_file: C0数据文件路径
        """
        self.monitor_interval = interval
        self.data_file = data_file
        self.logger.info(f"启动定期发布服务，间隔: {interval}秒")
        
        # 停止之前的定时器（如果存在）
        self.stop_periodic_publish()
        
        # 启动新的定时器
        self._schedule_next_publish()
    
    def _schedule_next_publish(self):
        """
        调度下一次发布
        参考FastBee_sdk.py的定时器调度机制
        """
        def publish_task():
            try:
                if self.connected:
                    success = self.publish_c0_data(getattr(self, 'data_file', "data/c0_parsed_data.json"))
                    if success:
                        self.logger.info("定期发布成功")
                    else:
                        self.logger.warning("定期发布失败")
                else:
                    self.logger.warning("MQTT未连接，跳过本次发布")
                    # 如果未连接，尝试重连
                    if self.retry_count < self.max_retry_count:
                        self.reconnect()
                        
            except Exception as e:
                self.logger.error(f"定期发布出错: {e}")
            
            # 调度下一次发布
            self._schedule_next_publish()
        
        # 创建定时器
        self.publish_timer = threading.Timer(self.monitor_interval, publish_task)
        self.publish_timer.daemon = True
        self.publish_timer.start()
    
    def create_c5_mqtt_message(self, c5_data: list) -> list:
        """
        创建C5 MQTT消息体
        
        Args:
            c5_data: C5解析数据
            
        Returns:
            C5 MQTT消息体
        """
        return c5_data

    def publish_c5_data(self, data_file: str = "data/c5_parsed_data.json") -> bool:
        """
        发布C5数据到MQTT
        
        Args:
            data_file: C5数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布C5消息")
            return False
        
        # 加载C5数据
        c5_data = self.load_c5_data(data_file)
        if not c5_data:
            return False
        
        # 创建C5消息体
        message = self.create_c5_mqtt_message(c5_data)
        
        try:
            # 发布C5数据到指定主题
            topic = "/196/D19822J4J92OT/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"C5数据发布成功到主题: {topic}")
                self.logger.debug(f"C5消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"C5数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布C5数据时发生错误: {e}")
            return False

    def publish_all_data(self, c0_file: str = "data/c0_parsed_data.json", c5_file: str = "data/c5_parsed_data.json") -> bool:
        """
        同时发布C0和C5数据
        
        Args:
            c0_file: C0数据文件路径
            c5_file: C5数据文件路径
            
        Returns:
            发布是否成功
        """
        success_c0 = self.publish_c0_data(c0_file)
        success_c5 = self.publish_c5_data(c5_file)
        
        return success_c0 and success_c5

    def start_periodic_publish_all(self, interval: int = 30, 
                                       c0_file: str = "data/c0_parsed_data.json", 
                                       c5_file: str = "data/c5_parsed_data.json",
                                       d0_file: str = "data/d0_parsed_data.json",
                                       d1_file: str = "data/d1_parsed_data.json",
                                       d2_file: str = "data/d2_parsed_data.json",
                                       d6_file: str = "data/d6_parsed_data.json",
                                       d7_file: str = "data/d7_parsed_data.json",
                                       d8_file: str = "data/d8_parsed_data.json"):
        """
        启动定期发布服务（同时发布所有类型数据）
        
        Args:
            interval: 发布间隔（秒）
            c0_file: C0数据文件路径
            c5_file: C5数据文件路径
            d0_file: D0数据文件路径
            d1_file: D1数据文件路径
            d2_file: D2数据文件路径
            d6_file: D6数据文件路径
            d7_file: D7数据文件路径
            d8_file: D8数据文件路径
        """
        self.monitor_interval = interval
        self.c0_file = c0_file
        self.c5_file = c5_file
        self.d0_file = d0_file
        self.d1_file = d1_file
        self.d2_file = d2_file
        self.d6_file = d6_file
        self.d7_file = d7_file
        self.d8_file = d8_file
        self.logger.info(f"启动定期发布服务（C0+C5+D0+D1+D2+D6+D7+D8），间隔: {interval}秒")
        
        # 停止之前的定时器（如果存在）
        self.stop_periodic_publish()
        
        # 启动新的定时器
        self._schedule_next_publish_all_types()

    def _schedule_next_publish_all(self):
        """
        调度下一次发布（同时发布C0和C5数据）
        """
        def publish_task():
            try:
                if self.connected:
                    success = self.publish_all_data(
                        getattr(self, 'c0_file', "data/c0_parsed_data.json"),
                        getattr(self, 'c5_file', "data/c5_parsed_data.json")
                    )
                    if success:
                        self.logger.info("定期发布成功（C0+C5）")
                    else:
                        self.logger.warning("定期发布失败（C0+C5）")
                else:
                    self.logger.warning("MQTT未连接，跳过本次发布")
                    # 如果未连接，尝试重连
                    if self.retry_count < self.max_retry_count:
                        self.reconnect()
                        
            except Exception as e:
                self.logger.error(f"定期发布出错: {e}")
            
            # 调度下一次发布
            self._schedule_next_publish_all()
        
        # 创建定时器
        self.publish_timer = threading.Timer(self.monitor_interval, publish_task)
        self.publish_timer.daemon = True
        self.publish_timer.start()
    
    def stop_periodic_publish(self):
        """
        停止定期发布服务
        参考FastBee_sdk.py的定时器管理
        """
        if self.publish_timer and self.publish_timer.is_alive():
            self.publish_timer.cancel()
            self.publish_timer = None
            self.logger.info("定期发布服务已停止")

def main():
    """
    主函数
    支持C0、C5、D0、D1、D2、D6、D7、D8数据推送服务
    """
    print("=== MQTT C0+C5+D0+D1+D2+D6+D7+D8 数据推送服务 ===")
    publisher = None
    
    try:
        # 创建MQTT推送服务实例
        publisher = MQTTPublisher()
        
        # 连接MQTT服务器
        publisher.connect()
        
        # 等待连接成功
        print("等待MQTT连接...")
        retry_count = 0
        max_wait_time = 30  # 最大等待30秒
        
        while not publisher.connected and retry_count < max_wait_time:
            print("-", end=" ", flush=True)
            time.sleep(1)
            retry_count += 1
        
        if not publisher.connected:
            print("\nMQTT连接超时，程序退出")
            return
        
        print("\nMQTT连接成功！")
        print("按 Ctrl+C 停止服务")
        
        # 发布一次所有类型数据测试
        success_c0 = publisher.publish_c0_data()
        success_c5 = publisher.publish_c5_data()
        success_d0 = publisher.publish_d0_data()
        success_d1 = publisher.publish_d1_data()
        success_d2 = publisher.publish_d2_data()
        success_d6 = publisher.publish_d6_data()
        success_d7 = publisher.publish_d7_data()
        success_d8 = publisher.publish_d8_data()
        
        if success_c0:
            publisher.logger.info("初始C0数据发布成功")
        else:
            publisher.logger.warning("初始C0数据发布失败")
            
        if success_c5:
            publisher.logger.info("初始C5数据发布成功")
        else:
            publisher.logger.warning("初始C5数据发布失败")
            
        if success_d0:
            publisher.logger.info("初始D0数据发布成功")
        else:
            publisher.logger.warning("初始D0数据发布失败")
            
        if success_d1:
            publisher.logger.info("初始D1数据发布成功")
        else:
            publisher.logger.warning("初始D1数据发布失败")
            
        if success_d2:
            publisher.logger.info("初始D2数据发布成功")
        else:
            publisher.logger.warning("初始D2数据发布失败")
            
        if success_d6:
            publisher.logger.info("初始D6数据发布成功")
        else:
            publisher.logger.warning("初始D6数据发布失败")
            
        if success_d7:
            publisher.logger.info("初始D7数据发布成功")
        else:
            publisher.logger.warning("初始D7数据发布失败")
            
        if success_d8:
            publisher.logger.info("初始D8数据发布成功")
        else:
            publisher.logger.warning("初始D8数据发布失败")
        
        # 启动定期发布服务（同时发布所有类型数据）
        publisher.start_periodic_publish_all(interval=30)
        
        # 主循环
        publisher.logger.info("MQTT推送服务已启动，开始主循环")
        
        while True:
            try:
                # 每10秒进行一次健康检查
                if not publisher.health_check():
                    publisher.logger.warning("健康检查失败，尝试重连...")
                    if publisher.retry_count < publisher.max_retry_count:
                        publisher.reconnect()
                
                # 打印连接状态（每60秒一次）
                if int(time.time()) % 60 == 0:
                    status = publisher.get_connection_status()
                    publisher.logger.info(f"连接状态: {status}")
                
                time.sleep(10)  # 主线程休眠10秒
                
            except KeyboardInterrupt:
                publisher.logger.info("收到中断信号，正在关闭服务...")
                break
            except Exception as e:
                publisher.logger.error(f"主循环异常: {e}")
                time.sleep(5)  # 异常时等待5秒再继续
        
    except Exception as e:
        print(f"程序启动异常: {e}")
    
    finally:
        # 清理资源
        if publisher:
            try:
                publisher.logger.info("正在清理资源...")
                publisher.stop_periodic_publish()
                publisher.disconnect()
                publisher.logger.info("程序已安全退出")
            except Exception as e:
                print(f"清理资源时出错: {e}")
        print("MQTT推送服务已停止")

if __name__ == '__main__':
    main()