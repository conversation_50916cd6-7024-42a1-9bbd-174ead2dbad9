# 串口规则处理系统

## 项目概述

这是一个串口通信和数据处理系统，用于处理串口设备的通信协议和数据解析。

## 项目结构

```
SerialPortRule/
├── src/                        # 源代码目录
│   ├── core/                   # 核心服务模块
│   │   └── simple_main.py      # 主要的串口服务程序
│   ├── parsers/                # 数据解析器模块
│   │   ├── c0_data_parser.py   # C0命令数据解析器
│   │   ├── c5_data_parser.py   # C5命令数据解析器
│   │   └── c59_data_parser.py  # C59命令数据解析器
│   └── services/               # 服务模块
│       ├── data_parser_service.py # 数据解析器服务（独立运行）
│       ├── mqtt_publisher.py   # MQTT推送服务
│       └── config_windows.py   # Windows配置文件
├── tools/                      # 工具脚本目录
│   ├── manage_service.sh       # 服务管理脚本
│   └── start_data_parser_service.sh # 数据解析器服务启动脚本
├── tests/                      # 测试文件目录
│   └── test_data_parser_service.py # 数据解析器服务测试
├── config/                     # 配置文件目录
│   ├── service.conf            # Linux服务配置文件
│   ├── service_windows.conf    # Windows服务配置文件
│   └── protocol.json           # 通信协议配置文件
├── data/                       # 数据目录
│   ├── realtime_data.json      # 实时数据文件
│   ├── c0_parsed_data.json     # C0解析后的数据
│   ├── c5_parsed_data.json     # C5解析后的数据
│   └── 59_parsed_data.json     # C59解析后的数据
├── logs/                       # 日志目录
├── docs/                       # 文档目录
├── scripts/                    # 脚本目录
│   ├── start_service.sh        # 主服务启动脚本
│   └── stop_service.sh         # 主服务停止脚本
├── deployment/                 # 部署相关文件
│   ├── data-parser.service     # 数据解析器服务配置文件
│   ├── serialport-service.service # 主服务配置文件
│   ├── serialport-logrotate.conf # 日志轮转配置
│   └── setup_logrotate.sh      # 日志轮转安装脚本
├── requirements.txt            # Python依赖包列表
└── README.md                   # 项目说明文档
```

## 功能特性

### 主要服务

1. **串口通信服务** (`simple_main.py`)
   - 处理串口设备通信
   - 执行启动序列和循环序列
   - 生成实时数据文件

2. **数据解析器服务** (`data_parser_service.py`)
   - 独立运行的数据处理服务
   - 监控数据文件变化
   - 自动触发相应的数据解析器
   - 支持C0、C5、C59三种数据格式解析

### 数据解析器

1. **C0数据解析器** (`c0_data_parser.py`)
   - 解析C0命令返回的数据
   - 生成标准格式的JSON输出

2. **C5数据解析器** (`c5_data_parser.py`)
   - 解析C5命令返回的版本信息
   - 支持BCD码转换和模块版本解析

3. **C59数据解析器** (`c59_data_parser.py`)
   - 解析C59命令返回的参数数据
   - 支持Float_32bit和Uint_32bit数据类型转换
   - 生成C0格式兼容的输出

## 安装和使用

### 环境要求

- Python 3.7+
- Linux操作系统

### 安装依赖

```bash
# 安装Python依赖包
pip3 install -r requirements.txt
```

### 运行服务

#### 1. 启动串口通信服务

```bash
python3 src/core/simple_main.py
```

#### 2. 启动数据解析器服务

使用启动脚本（推荐）：
```bash
./tools/start_data_parser_service.sh
```

或直接运行：
```bash
python3 src/services/data_parser_service.py
```

### 服务特性

#### 数据解析器服务特性

- **文件监控**: 自动监控`data/`目录下的数据文件变化
- **智能触发**: 
  - 当`realtime_data.json`文件更新时，自动运行C0和C5解析器
  - 当`59_data.json`文件更新时，自动运行C59解析器
- **防重复运行**: 避免同一解析器同时运行多个实例
- **初始解析**: 服务启动时自动执行一次完整解析
- **异步处理**: 使用异步编程提高处理效率

#### 数据流程

1. `src/core/simple_main.py` 生成 `realtime_data.json` 和 `59_data.json`
2. `src/services/data_parser_service.py` 监控文件变化
3. 自动触发相应的解析器：
   - `src/parsers/c0_data_parser.py` → `c0_parsed_data.json`
   - `src/parsers/c5_data_parser.py` → `c5_parsed_data.json`
   - `src/parsers/c59_data_parser.py` → `59_parsed_data.json`

## 配置说明

### 数据目录

默认数据目录为`data/`，包含以下文件：

- `realtime_data.json`: 串口服务生成的实时数据
- `59_data.json`: C59命令的专用数据文件
- `c0_parsed_data.json`: C0解析结果
- `c5_parsed_data.json`: C5解析结果
- `59_parsed_data.json`: C59解析结果

### 服务配置

数据解析器服务支持以下配置：

- 数据目录路径（默认：`data`）
- 文件监控间隔
- 解析器超时设置

## 开发说明

### 代码规范

- 使用相对路径引用数据文件
- 遵循PEP 8编码规范
- 添加适当的错误处理和日志输出

### 扩展新的解析器

1. 创建新的解析器文件（如`cx_data_parser.py`）
2. 在`data_parser_service.py`中添加相应的监控逻辑
3. 更新启动脚本和文档

## 故障排除

### 常见问题

1. **依赖包安装失败**
   ```bash
   pip3 install --upgrade pip
   pip3 install -r requirements.txt
   ```

2. **权限问题**
   ```bash
   chmod +x start_data_parser_service.sh
   ```

3. **数据文件不存在**
   - 确保`data/`目录存在
   - 检查串口服务是否正常运行

4. **解析器无响应**
   - 检查文件监控是否正常
   - 查看服务日志输出
   - 确认数据文件格式正确

### 日志和调试

服务运行时会输出详细的日志信息，包括：
- 文件变化检测
- 解析器启动和完成状态
- 错误信息和异常处理

## 更新日志

### v1.0.0
- 实现基础串口通信功能
- 添加C0、C5、C59数据解析器
- 创建独立的数据解析器服务
- 支持文件监控和自动触发
- 统一文件命名规范
- 使用相对路径提高可移植性

## 许可证

本项目采用MIT许可证。