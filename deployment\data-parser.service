[Unit]
Description=Data Parser Service for Serial Port Rule
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/home/<USER>/SerialPortRule
ExecStart=/usr/bin/python3 /home/<USER>/SerialPortRule/src/services/data_parser_service.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=data-parser

# 环境变量
Environment=PYTHONPATH=/home/<USER>/SerialPortRule
Environment=PYTHONUNBUFFERED=1

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/home/<USER>/SerialPortRule/data

[Install]
WantedBy=multi-user.target