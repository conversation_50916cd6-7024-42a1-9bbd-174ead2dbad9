#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
59数据帧处理工具
整合数据映射和格式转换功能，直接生成c0格式的59_parsed_data.json文件
"""

import json
import os
import re
import struct
from datetime import datetime
from typing import Dict, List, Tuple, Any

def parse_variable_definitions() -> Dict[int, Dict[str, str]]:
    """
    解析59数据帧对应关系的变量定义（内嵌数据）

    Returns:
        字典，键为通道号，值为包含数据类型和描述的字典
    """
    # 内嵌的59数据帧对应关系数据
    variable_definitions = """
Float_32bit	（比例系数）直流电压1001
Float_32bit	（kc系数）直流电压1002
Float_32bit	（积分系数）直流电压1003
Float_32bit	（微分系数）直流电压1004
Float_32bit	（输出上限）直流电压1005
Float_32bit	（输出下限）直流电压1006
Float_32bit	（积分上限）直流电压1007
Float_32bit	（积分下限）直流电压1008
Float_32bit	（积分初始值）直流电压1009
Float_32bit	（备用）直流电压1010
Float_32bit	（比例系数）直接电流1101
Float_32bit	（积分系数）直接电流1102
Float_32bit	（输出上限）直接电流1103
Float_32bit	（积分上限）直接电流1104
Float_32bit	（备用）直接电流1105
Float_32bit	（比例系数）间接电流有功1201
Float_32bit	（kc系数）间接电流有功1202
Float_32bit	（积分系数）间接电流有功1203
Float_32bit	（微分系数）间接电流有功1204
Float_32bit	（输出上限）间接电流有功1205
Float_32bit	（输出下限）间接电流有功1206
Float_32bit	（积分上限）间接电流有功1207
Float_32bit	（积分下限）间接电流有功1208
Float_32bit	（积分初始值）间接电流有功1209
Float_32bit	（备用）间接电流有功1210
Float_32bit	（比例系数）间接电流无功1301
Float_32bit	（kc系数）间接电流无功1302
Float_32bit	（积分系数）间接电流无功1303
Float_32bit	（微分系数）间接电流无功1304
Float_32bit	（输出上限）间接电流无功1305
Float_32bit	（输出下限）间接电流无功1306
Float_32bit	（积分上限）间接电流无功1307
Float_32bit	（积分下限）间接电流无功1308
Float_32bit	（积分初始值）间接电流无功1309
Float_32bit	（备用）间接电流无功1310
Float_32bit	（比例系数）相直流侧电压平衡控制1401
Float_32bit	（kc系数）相直流侧电压平衡控制1402
Float_32bit	（积分系数）相直流侧电压平衡控制1403
Float_32bit	（微分系数）相直流侧电压平衡控制1404
Float_32bit	（输出上限）相直流侧电压平衡控制1405
Float_32bit	（输出下限）相直流侧电压平衡控制1406
Float_32bit	（积分上限）相直流侧电压平衡控制1407
Float_32bit	（积分下限）相直流侧电压平衡控制1408
Float_32bit	（积分初始值）相直流侧电压平衡控制1409
Float_32bit	（备用）相直流侧电压平衡控制1410
Float_32bit	（比例系数）单元电压平衡控制1501
Float_32bit	（kc系数）单元电压平衡控制1502
Float_32bit	（积分系数）单元电压平衡控制1503
Float_32bit	（微分系数）单元电压平衡控制1504
Float_32bit	（输出上限）单元电压平衡控制1505
Float_32bit	（输出下限）单元电压平衡控制1506
Float_32bit	（积分上限）单元电压平衡控制1507
Float_32bit	（积分下限）单元电压平衡控制1508
Float_32bit	（积分初始值）单元电压平衡控制1509
Float_32bit	（备用）单元电压平衡控制1510
Float_32bit	（预充电电压）充电控制1601
Uint_32bit	（充电时间上限）充电控制1602
Uint_32bit	（充电时间下限）充电控制1603
Uint_32bit	（接触器超时控制）充电控制1604
Uint_32bit	（断路器超时控制）充电控制1605
Uint_32bit	（断路器合超时控制）充电控制1606
Float_32bit	（旁路接触器动作时间）充电控制1607
Float_32bit	（通道1校正系数）采样校正系数1701
Float_32bit	（通道2校正系数）采样校正系数1702
Float_32bit	（通道3校正系数）采样校正系数1703
Float_32bit	（通道4校正系数）采样校正系数1704
Float_32bit	（通道5校正系数）采样校正系数1705
Float_32bit	（通道6校正系数）采样校正系数1706
Float_32bit	（通道7校正系数）采样校正系数1707
Float_32bit	（通道8校正系数）采样校正系数1708
Float_32bit	（通道9校正系数）采样校正系数1709
Float_32bit	（通道10校正系数）采样校正系数1710
Float_32bit	（通道11校正系数）采样校正系数1711
Float_32bit	（通道12校正系数）采样校正系数1712
Float_32bit	（通道13校正系数）采样校正系数1713
Float_32bit	（通道14校正系数）采样校正系数1714
Float_32bit	（通道15校正系数）采样校正系数1715
Float_32bit	（通道16校正系数）采样校正系数1716
Float_32bit	（通道17校正系数）采样校正系数1717
Float_32bit	（通道18校正系数）采样校正系数1718
Float_32bit	（通道1偏移系数）采样偏移量1801
Float_32bit	（通道2偏移系数）采样偏移量1802
Float_32bit	（通道3偏移系数）采样偏移量1803
Float_32bit	（通道4偏移系数）采样偏移量1804
Float_32bit	（通道5偏移系数）采样偏移量1805
Float_32bit	（通道6偏移系数）采样偏移量1806
Float_32bit	（通道7偏移系数）采样偏移量1807
Float_32bit	（通道8偏移系数）采样偏移量1808
Float_32bit	（通道9偏移系数）采样偏移量1809
Float_32bit	（通道10偏移系数）采样偏移量1810
Float_32bit	（通道11偏移系数）采样偏移量1811
Float_32bit	（通道12偏移系数）采样偏移量1812
Float_32bit	（通道13偏移系数）采样偏移量1813
Float_32bit	（通道14偏移系数）采样偏移量1814
Float_32bit	（通道15偏移系数）采样偏移量1815
Float_32bit	（通道16偏移系数）采样偏移量A1816
Float_32bit	（通道17偏移系数）采样偏移量b1817
Float_32bit	（通道18偏移系数）采样偏移量C1818
Float_32bit	（额定电流kvar）系统参数1901
Float_32bit	（额定电压）系统参数1902
Float_32bit	（电流定标系数）系统参数1903
Float_32bit	（直流侧额定电压）系统参数1904
Float_32bit	（电网频率）系统参数1905
Float_32bit	（单元载波频率）系统参数1906
Float_32bit	（连接电感）系统参数1907
Float_32bit	（直流侧电压指令值）系统参数1908
Float_32bit	（斜坡速率）系统参数1909
Float_32bit	（斜坡输出最小值）系统参数1910
Float_32bit	（无功指令电流）系统参数1911
Float_32bit	（输出电流上限）系统参数1912
Float_32bit	（无功指令电流斜坡）系统参数1913
Uint_32bit	控制模式1  1914
Float_32bit	（电网一段电流CT变比）系统参数1915
Float_32bit	（SVG霍尔电流传感器变比）系统参数1916
Float_32bit	（电网二段电流CT变比）系统参数1917
Float_32bit	（SVG保护CT变比）系统参数1918
Float_32bit	（电网一段电压变比）系统参数1919
Float_32bit	（无功电流下限）系统参数1920
Float_32bit	（直流侧与无功电流系数）系统参数1921
Float_32bit	（无功电流滞后系数）系统参数1922
Float_32bit	（死区校正系数）系统参数1923
Float_32bit	（感性无功偏移系数）系统参数1924
Float_32bit	（感性无功增益）系统参数1925
Float_32bit	（容性无功偏移系数）系统参数1926
Float_32bit	（容性无功增益）系统参数1927
Float_32bit	（低电压穿越保护阀值1）系统参数1928
Float_32bit	（低电压穿越保护阀值2）系统参数1929
Float_32bit	（电网二段电压变比）系统参数1930
Float_32bit	（PT板RC移相角度补偿）系统参数1931
Float_32bit	（功率因数设定值）系统参数1932
Float_32bit	（上级网侧电流1段变比）系统参数1933
Float_32bit	（上级网侧电流2段变比）系统参数1934
Float_32bit	（电压基准）系统参数1935
Float_32bit	（电网电压指令）系统参数1936
Float_32bit	（电网电压指令斜坡）系统参数1937
Float_32bit	（SVG变压器变比）系统参数1938
Float_32bit	（电压补偿死区）系统参数1939
Float_32bit	（单元过压保护跳闸时间）系统参数1940
Float_32bit	（单元过压时间）系统参数1941
Float_32bit	（PWM板1级数）系统参数1942
Float_32bit	（PWM板2级数）系统参数1943
Float_32bit	（PWM板3级数）系统参数1944
Float_32bit	（输出电压d轴无功电流系数）系统参数1945
Float_32bit	（输出电压q轴无功电流系数）系统参数1946
Float_32bit	（PWM板4级数）系统参数1947
Float_32bit	（PWM板5级数）系统参数1948
Float_32bit	（PWM板6级数）系统参数1949
Float_32bit	（锁相环Kp）系统参数1950
Float_32bit	（锁相环Ki）系统参数1951
Float_32bit	（SVG升压变压器移相角度）系统参数1952
Float_32bit	（模拟板移相角）系统参数1953
Float_32bit	（传输延时补偿角度）系统参数1954
Float_32bit	（平衡控制补偿角度）系统参数1955
Float_32bit	（直流电流抑制Kp）系统参数1956
Float_32bit	（直流电流抑制Ki）系统参数1957
Float_32bit	（超前滞后环节Tb系数）系统参数1958
Float_32bit	（超前滞后环节Ta系数）系统参数1959
Uint_32bit	控制模式2  1960
Uint_32bit	控制模式3  1961
Float_32bit	（d轴正向辅助校正系数）系统参数1962
Float_32bit	（q轴正向辅助校正系数）系统参数1963
Float_32bit	（d轴负向辅助校正系数）系统参数1964
Float_32bit	（q轴负向辅助校正系数）系统参数1965
Float_32bit	（线电压有效值Ⅰ段过压报警值）SVG保护参数2001
Uint_32bit	（线电压有效值Ⅰ段过压报警时间）SVG保护参数2002
Float_32bit	（线电压有效值Ⅱ段过压保护值）SVG保护参数2003
Uint_32bit	（线电压有效值Ⅱ段过压保护时间）SVG保护参数2004
Float_32bit	（线电压幅值Ⅲ段过压保护值）SVG保护参数2005
Uint_32bit	（线电压幅值Ⅲ段过压保护时间）SVG保护参数2006
Float_32bit	（线电压瞬时值过压保护值）SVG保护参数2007
Float_32bit	（线电压有效值Ⅰ段欠压报警值）SVG保护参数2008
Uint_32bit	（线电压有效值Ⅰ段欠压报警时间）SVG保护参数2009
Float_32bit	（线电压有效值Ⅱ段欠压保护值）SVG保护参数2010
Uint_32bit	（线电压有效值Ⅱ段欠压保护时间）SVG保护参数2011
Float_32bit	（线电压幅值Ⅲ段欠压保护值）SVG保护参数2012
Uint_32bit	（线电压幅值Ⅲ段欠压保护时间）SVG保护参数2013
Float_32bit	（线电压幅度不平衡度保护值）SVG保护参数2014
Uint_32bit	（线电压幅度不平衡保护时间）SVG保护参数2015
Float_32bit	（线电压缺相保护值）SVG保护参数2016
Uint_32bit	（线电压缺相保护时间）SVG保护参数2017
Uint_32bit	（故障重新启动等待时间）SVG保护参数2018
Float_32bit	（输出电流有效值Ⅰ段过流报警值）SVG保护参数2019
Uint_32bit	（输出电流有效值Ⅰ段过流报警时间）SVG保护参数2020
Float_32bit	（输出电流有效值Ⅱ段过流保护值）SVG保护参数2021
Uint_32bit	（输出电流有效值Ⅱ段过流保护时间）SVG保护参数2022
Float_32bit	（输出电流瞬时值过流封脉冲保护值）SVG保护参数2023
Float_32bit	（输出电流缺相保护值）SVG保护参数2024
Uint_32bit	（输出电流缺相保护时间）SVG保护参数2025
Uint_32bit	（单元电压过压保护值）SVG保护参数2026
Uint_32bit	（单元电压不平衡保护值）SVG保护参数2027
Float_32bit	（输出电流瞬时值过流跳闸保护值）SVG保护参数2028
Uint_32bit	（输出电流瞬时值过流跳闸时间）SVG保护参数2029
Uint_32bit	（连续故障次数）SVG保护参数2030
Uint_32bit	（故障复位超时时间）SVG保护参数2031
Float_32bit	（霍尔传感器故障保护定值）SVG保护参数2032
Float_32bit	（输出电流(CT采样)瞬时值过流跳闸保护值）SVG保护参数2033
Uint_32bit	（输出电流(CT采样)瞬时值过流跳闸时间）SVG保护参数2034
Float_32bit	（零序电流瞬时值过流保护定值）SVG保护参数2035
Uint_32bit	（零序电流过流保护时间）SVG保护参数2036
Uint_32bit	保护功能使能1  2101
Uint_32bit	保护功能使能2 2102
Uint_32bit	故障录波参数 2201
Uint_32bit	故障录波设置 2202
Uint_32bit	故障录波参数 2203
Uint_32bit	谐波控制参数 2301
Uint_32bit	谐波控制参数 2302
Uint_32bit	滤波使能控制 2303
Float_32bit	（滤波通道1输出电流上限）谐波控制参数2304
Float_32bit	（滤波通道2输出电流上限）谐波控制参数2305
Float_32bit	（滤波通道3输出电流上限）谐波控制参数2306
Float_32bit	（滤波通道4输出电流上限）谐波控制参数2307
Float_32bit	（滤波通道5输出电流上限）谐波控制参数2308
Float_32bit	（滤波通道6输出电流上限）谐波控制参数2309
Float_32bit	（滤波通道7输出电流上限）谐波控制参数2310
Float_32bit	（滤波通道8输出电流上限）谐波控制参数2311
Float_32bit	（滤波通道1指令电流）谐波控制参数2312
Float_32bit	（滤波通道2指令电流）谐波控制参数2313
Float_32bit	（滤波通道3指令电流）谐波控制参数2314
Float_32bit	（滤波通道4指令电流）谐波控制参数2315
Float_32bit	（滤波通道5指令电流）谐波控制参数2316
Float_32bit	（滤波通道6指令电流）谐波控制参数2317
Float_32bit	（滤波通道7指令电流）谐波控制参数2318
Float_32bit	（滤波通道8指令电流）谐波控制参数2319
Float_32bit	（H1通道Kp系数）谐波控制参数2320
Float_32bit	（H2通道Kp系数）谐波控制参数2321
Float_32bit	（H3通道Kp系数）谐波控制参数2322
Float_32bit	（H4通道Kp系数）谐波控制参数2323
Float_32bit	（H5通道Kp系数）谐波控制参数2324
Float_32bit	（H6通道Kp系数）谐波控制参数2325
Float_32bit	（H7通道Kp系数）谐波控制参数2326
Float_32bit	（H8通道Kp系数）谐波控制参数2327
Float_32bit	（H1通道Ki系数）谐波控制参数2328
Float_32bit	（H2通道Ki系数）谐波控制参数2329
Float_32bit	（H3通道Ki系数）谐波控制参数2330
Float_32bit	（H4通道Ki系数）谐波控制参数2331
Float_32bit	（H5通道Ki系数）谐波控制参数2332
Float_32bit	（H6通道Ki系数）谐波控制参数2333
Float_32bit	（H7通道Ki系数）谐波控制参数2334
Float_32bit	（H8通道Ki系数）谐波控制参数2335
Float_32bit	（调节比例系数）电网电压控制2401
Float_32bit	（调节Kc系数）电网电压控制2402
Float_32bit	（调节积分系数）电网电压控制2403
Float_32bit	（调节微分系数）电网电压控制2404
Float_32bit	（输出上限）电网电压控制2405
Float_32bit	（输出下限）电网电压控制2406
Float_32bit	（积分上限）电网电压控制2407
Float_32bit	（积分下限）电网电压控制2408
Float_32bit	（积分初始值）电网电压控制2409
Float_32bit	（备用1）电网电压控制2410
Float_32bit	（调节比例系数）电网电压无功控制2501
Float_32bit	（调节Kc系数）电网电压无功控制2502
Float_32bit	（调节积分系数）电网电压无功控制2503
Float_32bit	（调节微分系数）电网电压无功控制2504
Float_32bit	（输出上限）电网电压无功控制2505
Float_32bit	（输出下限）电网电压无功控制2506
Float_32bit	（积分上限）电网电压无功控制2507
Float_32bit	（积分下限）电网电压无功控制2508
Float_32bit	（积分初始值）电网电压无功控制2509
Float_32bit	（备用1）电网电压无功控制2510
"""

    variables = {}

    try:
        # 按行分割
        lines = variable_definitions.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 使用制表符分割
            parts = line.split('\t')
            if len(parts) >= 2:
                data_type = parts[0].strip()
                description_and_channel = parts[1].strip()

                # 从描述中提取通道号（最后的数字）
                match = re.search(r'(\d+)$', description_and_channel)
                if match:
                    channel = int(match.group(1))
                    description = description_and_channel[:match.start()].strip()

                    variables[channel] = {
                        'data_type': data_type,
                        'description': description
                    }

    except Exception as e:
        print(f"解析变量定义时出错: {e}")

    return variables

def extract_59_frames(json_file_path: str) -> List[Dict[str, Any]]:
    """
    从realtime_data.json中提取所有59命令的响应帧

    Args:
        json_file_path: JSON文件路径

    Returns:
        59命令响应帧列表
    """
    frames_59 = []

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        for frame in data:
            if frame.get('command') == '59' and 'channel' in frame:
                frames_59.append(frame)

    except Exception as e:
        print(f"读取JSON文件时出错: {e}")

    return frames_59

def hex_to_float32(hex_str: str) -> float:
    """
    将十六进制字符串转换为32位浮点数

    Args:
        hex_str: 十六进制字符串（如 "40C00000"）

    Returns:
        浮点数值
    """
    try:
        # 移除0x前缀（如果有）
        hex_str = hex_str.replace('0x', '')
        # 确保是8位十六进制
        hex_str = hex_str.zfill(8)
        # 转换为字节
        bytes_data = bytes.fromhex(hex_str)
        # 解包为浮点数（大端序）
        return struct.unpack('>f', bytes_data)[0]
    except:
        return 0.0

def hex_to_uint32(hex_str: str) -> int:
    """
    将十六进制字符串转换为32位无符号整数

    Args:
        hex_str: 十六进制字符串（如 "00000000"）

    Returns:
        整数值
    """
    try:
        # 移除0x前缀（如果有）
        hex_str = hex_str.replace('0x', '')
        # 确保是8位十六进制
        hex_str = hex_str.zfill(8)
        # 转换为整数
        return int(hex_str, 16)
    except:
        return 0

def process_59_data_to_c0_format():
    """
    处理59数据帧并直接转换为c0格式
    """
    # 文件路径
    json_file = 'data/realtime_data.json'
    output_file = 'data/59_parsed_data.json'

    print("开始处理59数据帧...")

    # 解析变量定义
    print("解析变量定义...")
    variables = parse_variable_definitions()
    print(f"解析到 {len(variables)} 个变量定义")

    # 提取59响应帧
    print("提取59命令响应帧...")
    frames_59 = extract_59_frames(json_file)
    print(f"提取到 {len(frames_59)} 个59命令响应帧")

    # 检查是否为第一次解析（文件不存在）
    is_first_parse = not os.path.exists(output_file)
    existing_write_values = {}
    
    if is_first_parse:
        print("首次解析，writeValue将设置为解析值")
    else:
        print("非首次解析，读取现有writeValue值")
        # 读取现有文件中的writeValue值
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
                for item in existing_data:
                    existing_write_values[item['id']] = item.get('writeValue', '')
        except Exception as e:
            print(f"读取现有文件失败: {e}")
            existing_write_values = {}
    
    # 处理数据并转换为c0格式
    print("处理数据并转换为c0格式...")
    converted_data = []

    for frame in frames_59:
        channel = frame.get('channel')
        if channel in variables:
            var_info = variables[channel]
            data_type = var_info['data_type']
            description = var_info['description']

            # 获取数据十六进制值
            data_hex_formatted = frame.get('data_hex_formatted', [])
            if len(data_hex_formatted) >= 2:
                # 第二个元素通常是数据值
                hex_value = data_hex_formatted[1]

                # 根据数据类型转换值
                if data_type == 'Float_32bit':
                    parsed_value = hex_to_float32(hex_value)
                elif data_type == 'Uint_32bit':
                    parsed_value = hex_to_uint32(hex_value)
                else:
                    parsed_value = hex_value

                # 将timestamp转换为可读的时间格式
                timestamp = datetime.fromtimestamp(frame.get('timestamp', 0))
                ts_str = timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # 保留3位毫秒

                # 构建c0格式的数据项
                item_id = f"SVG_{channel}"
                # 只在第一次解析时，writeValue设置为解析值；后续解析时保留现有值
                if is_first_parse:
                    write_value = str(parsed_value)
                else:
                    write_value = existing_write_values.get(item_id, "")
                
                converted_item = {
                    "id": item_id,
                    "name": description,
                    "ts": ts_str,
                    "value": str(parsed_value),
                    "writeValue": write_value
                }

                converted_data.append(converted_item)

    # 按通道号排序
    converted_data.sort(key=lambda x: int(x['id'].split('_')[1]))

    # 保存结果
    print(f"保存结果到 {output_file}...")
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, ensure_ascii=False, indent=4)

        print(f"处理完成！共处理 {len(converted_data)} 个数据项")
        print(f"输出文件：{output_file}")

        # 显示前几个结果作为示例
        if converted_data:
            print("\n前5个处理结果示例:")
            for i, item in enumerate(converted_data[:5]):
                print(f"{i+1}. ID: {item['id']}, Name: {item['name']}, Value: {item['value']}")

        # 统计数据类型分布
        float_count = sum(1 for item in converted_data if '.' in item['value'])
        int_count = len(converted_data) - float_count
        print(f"\n数据类型统计:")
        print(f"Float类型: {float_count} 个")
        print(f"Integer类型: {int_count} 个")

    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == '__main__':
    process_59_data_to_c0_format()