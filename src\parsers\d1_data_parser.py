#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
D1命令数据解析器
根据D0-D1-D2单元直流侧电压协议解析文档将十六进制数据转换为B相单元直流侧电压JSON格式
"""

import json
from datetime import datetime
from typing import List, Dict, Any


class D1DataParser:
    """
    D1命令数据解析器类
    用于解析D1命令返回的B相单元直流侧电压数据
    """
    
    def __init__(self):
        """
        初始化解析器，定义B相单元映射关系
        """
        # B相单元映射（根据物模型表）
        self.unit_mapping = {
            1: {"id": "HMI_30290", "name": "B相单元直流侧电压（B1）"},
            2: {"id": "HMI_30291", "name": "B相单元直流侧电压（B2）"},
            3: {"id": "HMI_30292", "name": "B相单元直流侧电压（B3）"},
            4: {"id": "HMI_30293", "name": "B相单元直流侧电压（B4）"},
            5: {"id": "HMI_30294", "name": "B相单元直流侧电压（B5）"},
            6: {"id": "HMI_30295", "name": "B相单元直流侧电压（B6）"},
            7: {"id": "HMI_30296", "name": "B相单元直流侧电压（B7）"},
            8: {"id": "HMI_30297", "name": "B相单元直流侧电压（B8）"},
            9: {"id": "HMI_30298", "name": "B相单元直流侧电压（B9）"},
            10: {"id": "HMI_30299", "name": "B相单元直流侧电压（B10）"},
            11: {"id": "HMI_30300", "name": "B相单元直流侧电压（B11）"},
            12: {"id": "HMI_30301", "name": "B相单元直流侧电压（B12）"}
        }
    
    def parse_voltage_response(self, raw_hex: str) -> Dict[str, Any]:
        """
        解析D1协议的电压响应数据
        
        Args:
            raw_hex: 原始十六进制字符串
            
        Returns:
            dict: 包含解析结果的字典
        """
        # 移除空格并转换为字节数组
        raw_hex = raw_hex.replace(" ", "")
        if len(raw_hex) % 2 != 0:
            raise ValueError("十六进制字符串长度必须为偶数")
            
        data = bytes.fromhex(raw_hex)
        
        # 验证最小长度（帧头+地址+功能码+命令码+数据长度+36字节电压+校验+帧尾）
        if len(data) < 45:
            raise ValueError(f"数据长度不足，至少需要45字节，实际为{len(data)}字节")
        
        # 验证帧头帧尾
        if data[0:2] != b'\xEB\x90':
            raise ValueError("无效的帧头")
        if data[-2:] != b'\xAA\xAB':
            raise ValueError("无效的帧尾")
        
        # 验证命令码
        if data[4] != 0xD1:
            raise ValueError(f"命令码不匹配，期望0xD1，实际为0x{data[4]:02X}")
        
        # 验证数据长度
        data_length = data[5]
        if data_length != 0x24:  # 36字节
            raise ValueError(f"数据长度不匹配，期望0x24，实际为0x{data_length:02X}")
        
        # 提取电压数据（从第10字节开始，共36字节）
        voltage_data = data[10:46]
        
        # 解析每个单元的电压（每2字节一个单元，共18个单元，取前12个）
        voltages = []
        for i in range(12):  # 解析前12个单元
            pos = i * 2
            if pos + 1 < len(voltage_data):
                # 小端序转换：低字节 + 高字节 * 256
                value = voltage_data[pos] + (voltage_data[pos + 1] << 8)
                
                # 判断旁路状态（11-12单元数据为0表示旁路）
                status = "旁路" if value == 0 and i >= 10 else "正常"
                
                voltages.append({
                    'unit': i + 1,
                    'voltage': value,
                    'status': status,
                    'raw_value': value
                })
        
        return {
            'phase': 'B',
            'command': 'D1',
            'voltages': voltages,
            'raw_data': raw_hex
        }
    
    def parse_d1_data(self, raw_hex: str, timestamp: str = None) -> List[Dict[str, Any]]:
        """
        解析D1命令的raw_hex数据
        
        Args:
            raw_hex: D1命令返回的原始十六进制字符串
            timestamp: 时间戳，如果不提供则使用当前时间
            
        Returns:
            List[Dict]: 符合MQTT推送格式的数据列表
        """
        if not timestamp:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
        result = []
        
        if not raw_hex:
            raise ValueError("D1命令数据格式错误，数据不能为空")
        
        # 解析数据
        parsed_data = self.parse_voltage_response(raw_hex)
        
        # 生成MQTT消息格式
        for voltage_info in parsed_data['voltages']:
            unit_num = voltage_info['unit']
            if unit_num in self.unit_mapping:
                unit_config = self.unit_mapping[unit_num]
                result.append({
                    "id": unit_config["id"],
                    "name": unit_config["name"],
                    "ts": timestamp,
                    "value": str(voltage_info['voltage'])  # 转换为字符串以保持一致性
                })
        
        return result
    
    def parse_d1_data_to_json(self, raw_hex: str, timestamp: str = None,
                            output_file: str = None, pretty_print: bool = True) -> str:
        """
        解析D1命令数据并输出为JSON字符串或保存到文件
        
        Args:
            raw_hex: D1命令返回的原始十六进制字符串
            timestamp: 时间戳
            output_file: 输出文件路径，如果不提供则返回JSON字符串
            pretty_print: 是否格式化输出JSON
            
        Returns:
            JSON字符串
        """
        parsed_data = self.parse_d1_data(raw_hex, timestamp)
        
        if pretty_print:
            json_str = json.dumps(parsed_data, ensure_ascii=False, indent=4)
        else:
            json_str = json.dumps(parsed_data, ensure_ascii=False)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"解析结果已保存到: {output_file}")
        
        return json_str


def main():
    """
    主函数，从realtime_data.json文件中读取D1命令数据并解析
    """
    parser = D1DataParser()
    
    # 读取realtime_data.json文件
    realtime_data_file = "data/realtime_data.json"
    
    try:
        with open(realtime_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("D1命令数据解析器 - 从realtime_data.json读取数据")
        print("=" * 60)
        
        # 查找D1命令数据
        d1_data_found = False
        for item in data:
            if item.get('command') == 'D1' and item.get('success') and item.get('raw_hex'):
                d1_data_found = True
                
                print(f"找到D1命令数据:")
                print(f"时间戳: {item.get('timestamp')}")
                print(f"原始数据: {item.get('raw_hex')}")
                print()
                
                # 解析数据
                timestamp_str = datetime.fromtimestamp(item['timestamp']).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                json_result = parser.parse_d1_data_to_json(
                    item['raw_hex'],
                    timestamp=timestamp_str
                )
                print("解析结果:")
                print(json_result)
                
                # 保存到文件
                output_file = "data/d1_parsed_data.json"
                parser.parse_d1_data_to_json(
                    item['raw_hex'],
                    timestamp=timestamp_str,
                    output_file=output_file
                )
                
                break  # 只处理第一个找到的D1数据
        
        if not d1_data_found:
            print("在realtime_data.json中未找到有效的D1命令数据")
            print("请确保文件中包含command='D1'且success=true的数据项")
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {realtime_data_file}")
        print("请确保realtime_data.json文件存在")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"处理错误: {e}")


if __name__ == "__main__":
    main()